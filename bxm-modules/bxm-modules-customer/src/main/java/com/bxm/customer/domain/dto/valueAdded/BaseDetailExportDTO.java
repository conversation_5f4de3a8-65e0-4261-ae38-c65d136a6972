package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 明细导出DTO基类
 *
 * 包含社保明细和个税明细导出的共同字段，用于优雅抽象和代码复用。
 * 共同字段包括：方式、姓名、身份证号、手机号、备注、社保基数
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("明细导出DTO基类")
public abstract class BaseDetailExportDTO {

    /** 方式 */
    @Excel(name = "方式", sort = 1)
    @ApiModelProperty(value = "方式")
    private String operationType;

    /** 姓名 */
    @Excel(name = "姓名", sort = 2)
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /** 身份证号 */
    @Excel(name = "身份证号", sort = 3)
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 手机号 */
    @Excel(name = "手机号", sort = 4)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 备注 */
    @Excel(name = "备注", sort = 5)
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 社保基数 */
    @Excel(name = "社保基数", sort = 6)
    @ApiModelProperty(value = "社保基数")
    private String socialInsuranceBase;
}
