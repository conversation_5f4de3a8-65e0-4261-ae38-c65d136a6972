package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 个税明细导出DTO
 *
 * 用于导出个税明细Excel文件，继承基类的共同字段（方式、姓名、身份证号、手机号、备注、社保基数），
 * 并包含个税特有字段：应发工资、公积金个人缴存金额、各类保险、其他等
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("个税明细导出DTO")
public class PersonalTaxDetailExportDTO extends BaseDetailExportDTO {

    /** 应发工资 */
    @Excel(name = "应发工资", sort = 7)
    @ApiModelProperty(value = "应发工资")
    private String grossSalary;

    /** 公积金个人缴存金额 */
    @Excel(name = "公积金个人缴存金额", sort = 8)
    @ApiModelProperty(value = "公积金个人缴存金额")
    private String housingFundPersonalAmount;

    /** 养老保险 */
    @Excel(name = "养老保险", sort = 9)
    @ApiModelProperty(value = "养老保险")
    private String yangLaoInsurance;

    /** 失业保险 */
    @Excel(name = "失业保险", sort = 10)
    @ApiModelProperty(value = "失业保险")
    private String shiYeInsurance;

    /** 工伤保险 */
    @Excel(name = "工伤保险", sort = 11)
    @ApiModelProperty(value = "工伤保险")
    private String gongShangInsurance;

    /** 医疗保险 */
    @Excel(name = "医疗保险", sort = 12)
    @ApiModelProperty(value = "医疗保险")
    private String yiLiaoInsurance;

    /** 生育保险 */
    @Excel(name = "生育保险", sort = 13)
    @ApiModelProperty(value = "生育保险")
    private String shengYuInsurance;

    /** 其他 */
    @Excel(name = "其他", sort = 14)
    @ApiModelProperty(value = "其他")
    private String other;
}
