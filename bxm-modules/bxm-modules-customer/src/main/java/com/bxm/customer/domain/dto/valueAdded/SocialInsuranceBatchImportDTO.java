package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 社保批量导入DTO
 *
 * 用于Excel文件批量导入社保员工信息，基于SocialInsuranceDetailExportDTO结构
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("社保批量导入DTO")
public class SocialInsuranceBatchImportDTO {

    /** 方式 */
    @Excel(name = "方式", sort = 1)
    @ApiModelProperty(value = "方式：增员/更正/减员")
    private String operationType;

    /** 姓名 */
    @Excel(name = "姓名", sort = 2)
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /** 身份证号 */
    @Excel(name = "身份证号", sort = 3)
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 手机号 */
    @Excel(name = "手机号", sort = 4)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 备注 */
    @Excel(name = "备注", sort = 5)
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 社保基数 */
    @Excel(name = "社保基数", sort = 6)
    @ApiModelProperty(value = "社保基数")
    private String socialInsuranceBase;

    /** 养老 */
    @Excel(name = "养老", sort = 7)
    @ApiModelProperty(value = "养老：是/否")
    private String yangLao;

    /** 失业 */
    @Excel(name = "失业", sort = 8)
    @ApiModelProperty(value = "失业：是/否")
    private String shiYe;

    /** 工伤 */
    @Excel(name = "工伤", sort = 9)
    @ApiModelProperty(value = "工伤：是/否")
    private String gongShang;

    /** 医疗 */
    @Excel(name = "医疗", sort = 10)
    @ApiModelProperty(value = "医疗：是/否")
    private String yiLiao;

    /** 生育 */
    @Excel(name = "生育", sort = 11)
    @ApiModelProperty(value = "生育：是/否")
    private String shengYu;


    /** 行号（用于错误定位） */
    @ApiModelProperty(value = "行号")
    private Integer rowNumber;
}
