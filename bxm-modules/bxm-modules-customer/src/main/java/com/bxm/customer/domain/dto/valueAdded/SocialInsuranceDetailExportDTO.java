package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 社保明细导出DTO
 *
 * 用于导出社保明细Excel文件，继承基类的共同字段（方式、姓名、身份证号、手机号、备注、社保基数、
 * 养老、失业、工伤、医疗、生育），社保业务无需额外字段
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("社保明细导出DTO")
public class SocialInsuranceDetailExportDTO extends BaseDetailExportDTO {
    // 社保明细导出DTO继承基类的所有字段，无需额外字段
}
