package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 社保明细导出DTO
 *
 * 用于导出社保明细Excel文件，继承基类的共同字段（方式、姓名、身份证号、手机号、备注、社保基数），
 * 并包含社保特有字段：养老、失业、工伤、医疗、生育等
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("社保明细导出DTO")
public class SocialInsuranceDetailExportDTO extends BaseDetailExportDTO {

    /** 养老 */
    @Excel(name = "养老", sort = 7)
    @ApiModelProperty(value = "养老")
    private String yangLao;

    /** 失业 */
    @Excel(name = "失业", sort = 8)
    @ApiModelProperty(value = "失业")
    private String shiYe;

    /** 工伤 */
    @Excel(name = "工伤", sort = 9)
    @ApiModelProperty(value = "工伤")
    private String gongShang;

    /** 医疗 */
    @Excel(name = "医疗", sort = 10)
    @ApiModelProperty(value = "医疗")
    private String yiLiao;

    /** 生育 */
    @Excel(name = "生育", sort = 11)
    @ApiModelProperty(value = "生育")
    private String shengYu;
}
